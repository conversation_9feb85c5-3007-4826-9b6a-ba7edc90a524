/* TDesign 暗色主题配置 */
:root {
  /* TDesign 主题色彩变量覆盖 */
  --td-brand-color: #3b82f6;
  --td-brand-color-1: #1d4ed8;
  --td-brand-color-2: #2563eb;
  --td-brand-color-3: #3b82f6;
  --td-brand-color-4: #60a5fa;
  --td-brand-color-5: #93c5fd;
  --td-brand-color-6: #bfdbfe;
  --td-brand-color-7: #dbeafe;
  --td-brand-color-8: #eff6ff;
  --td-brand-color-9: #f8fafc;
  --td-brand-color-10: #ffffff;

  /* 暗色主题背景色 */
  --td-bg-color-page: #000000;
  --td-bg-color-container: #111111;
  --td-bg-color-container-hover: #1a1a1a;
  --td-bg-color-container-active: #222222;
  --td-bg-color-container-select: #1a1a1a;
  --td-bg-color-secondarycontainer: #1a1a1a;
  --td-bg-color-component: #1a1a1a;
  --td-bg-color-component-hover: #222222;
  --td-bg-color-component-active: #2a2a2a;
  --td-bg-color-component-disabled: #0f0f0f;

  /* 文字颜色 */
  --td-text-color-primary: #ffffff;
  --td-text-color-secondary: #cccccc;
  --td-text-color-placeholder: #666666;
  --td-text-color-disabled: #444444;
  --td-text-color-anti: #000000;
  --td-text-color-brand: #3b82f6;

  /* 边框颜色 */
  --td-border-level-1-color: #333333;
  --td-border-level-2-color: #444444;
  --td-component-border: #333333;
  --td-component-stroke: #333333;

  /* 阴影 */
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05), 0 4px 5px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05), 0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 5px 5px -3px rgba(0, 0, 0, 0.1);
  --td-shadow-3: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

  /* 成功色 */
  --td-success-color: #10b981;
  --td-success-color-1: #059669;
  --td-success-color-2: #047857;
  --td-success-color-3: #065f46;
  --td-success-color-4: #064e3b;
  --td-success-color-5: #022c22;
  --td-success-color-6: #dcfce7;
  --td-success-color-7: #bbf7d0;
  --td-success-color-8: #86efac;
  --td-success-color-9: #4ade80;
  --td-success-color-10: #22c55e;

  /* 警告色 */
  --td-warning-color: #f59e0b;
  --td-warning-color-1: #d97706;
  --td-warning-color-2: #b45309;
  --td-warning-color-3: #92400e;
  --td-warning-color-4: #78350f;
  --td-warning-color-5: #451a03;
  --td-warning-color-6: #fef3c7;
  --td-warning-color-7: #fde68a;
  --td-warning-color-8: #fcd34d;
  --td-warning-color-9: #fbbf24;
  --td-warning-color-10: #f59e0b;

  /* 错误色 */
  --td-error-color: #ef4444;
  --td-error-color-1: #dc2626;
  --td-error-color-2: #b91c1c;
  --td-error-color-3: #991b1b;
  --td-error-color-4: #7f1d1d;
  --td-error-color-5: #450a0a;
  --td-error-color-6: #fecaca;
  --td-error-color-7: #fca5a5;
  --td-error-color-8: #f87171;
  --td-error-color-9: #ef4444;
  --td-error-color-10: #dc2626;
}

/* 全局暗色主题样式 */
.t-layout {
  background-color: var(--td-bg-color-page) !important;
  color: var(--td-text-color-primary) !important;
}

.t-layout__header {
  background-color: var(--td-bg-color-container) !important;
  border-bottom: 1px solid var(--td-border-level-1-color) !important;
}

.t-layout__sider {
  background-color: var(--td-bg-color-container) !important;
  border-right: 1px solid var(--td-border-level-1-color) !important;
}

.t-layout__content {
  background-color: var(--td-bg-color-page) !important;
}

.t-menu {
  background-color: transparent !important;
}

.t-menu__item {
  color: var(--td-text-color-secondary) !important;
}

.t-menu__item:hover {
  background-color: var(--td-bg-color-component-hover) !important;
  color: var(--td-text-color-primary) !important;
}

.t-menu__item.t-is-active {
  background-color: var(--td-brand-color) !important;
  color: white !important;
}

.t-card {
  background-color: var(--td-bg-color-container) !important;
  border: 1px solid var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-input {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-input:focus {
  border-color: var(--td-brand-color) !important;
}

.t-textarea {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-textarea:focus {
  border-color: var(--td-brand-color) !important;
}

.t-select {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-table {
  background-color: var(--td-bg-color-container) !important;
  color: var(--td-text-color-primary) !important;
}

.t-table__header {
  background-color: var(--td-bg-color-secondarycontainer) !important;
}

.t-table tr:hover {
  background-color: var(--td-bg-color-component-hover) !important;
}

.t-button--variant-base {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-button--variant-base:hover {
  background-color: var(--td-bg-color-component-hover) !important;
}

.t-button--theme-primary {
  background-color: var(--td-brand-color) !important;
  border-color: var(--td-brand-color) !important;
}

.t-button--theme-primary:hover {
  background-color: var(--td-brand-color-1) !important;
  border-color: var(--td-brand-color-1) !important;
}

/* 下拉菜单暗色主题 */
.t-dropdown__menu {
  background-color: var(--td-bg-color-container) !important;
  border: 1px solid var(--td-border-level-1-color) !important;
  box-shadow: var(--td-shadow-2) !important;
}

.t-dropdown__item {
  color: var(--td-text-color-primary) !important;
}

.t-dropdown__item:hover {
  background-color: var(--td-bg-color-component-hover) !important;
}

/* 对话框暗色主题 */
.t-dialog {
  background-color: var(--td-bg-color-container) !important;
  color: var(--td-text-color-primary) !important;
}

.t-dialog__header {
  border-bottom: 1px solid var(--td-border-level-1-color) !important;
}

.t-dialog__footer {
  border-top: 1px solid var(--td-border-level-1-color) !important;
}

/* 消息提示暗色主题 */
.t-message {
  background-color: var(--td-bg-color-container) !important;
  color: var(--td-text-color-primary) !important;
  border: 1px solid var(--td-border-level-1-color) !important;
}

/* 标签暗色主题 */
.t-tag {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-tag--theme-primary {
  background-color: var(--td-brand-color) !important;
  color: white !important;
}

/* 分页器暗色主题 */
.t-pagination {
  color: var(--td-text-color-primary) !important;
}

.t-pagination__btn {
  background-color: var(--td-bg-color-component) !important;
  border-color: var(--td-border-level-1-color) !important;
  color: var(--td-text-color-primary) !important;
}

.t-pagination__btn:hover {
  background-color: var(--td-bg-color-component-hover) !important;
}

.t-pagination__btn.t-is-current {
  background-color: var(--td-brand-color) !important;
  border-color: var(--td-brand-color) !important;
  color: white !important;
}

/* 统计数值暗色主题 */
.t-statistic {
  color: var(--td-text-color-primary) !important;
}

.t-statistic__title {
  color: var(--td-text-color-secondary) !important;
}

.t-statistic__content {
  color: var(--td-text-color-primary) !important;
}
