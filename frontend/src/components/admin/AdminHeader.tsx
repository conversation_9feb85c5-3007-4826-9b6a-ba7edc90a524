
import { Link, useLocation } from 'react-router-dom';
import {
  Breadcrumb,
  Dropdown,
  Button,
  Space,
  Avatar
} from 'tdesign-react';
import {
  UserIcon,
  LogoutIcon,
  SettingIcon,
  HomeIcon
} from 'tdesign-icons-react';
import { ADMIN_NAV_ITEMS } from '@/constants';

const { BreadcrumbItem } = Breadcrumb;

export default function AdminHeader() {
  const location = useLocation();



  const getBreadcrumbItems = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const items = [{ name: 'Dashboard', path: '/admin' }];

    if (pathSegments.length > 1) {
      const currentItem = ADMIN_NAV_ITEMS.find(item =>
        location.pathname === item.path ||
        (item.path !== '/admin' && location.pathname.startsWith(`${item.path}/`))
      );
      if (currentItem && currentItem.path !== '/admin') {
        items.push({ name: currentItem.name, path: currentItem.path });
      }
    }

    return items;
  };

  const dropdownOptions = [
    {
      content: (
        <Space>
          <UserIcon />
          <span>个人资料</span>
        </Space>
      ),
      value: 'profile'
    },
    {
      content: (
        <Space>
          <SettingIcon />
          <span>设置</span>
        </Space>
      ),
      value: 'settings'
    },
    {
      content: (
        <Space>
          <HomeIcon />
          <span>返回博客</span>
        </Space>
      ),
      value: 'blog'
    },
    {
      content: (
        <Space>
          <LogoutIcon />
          <span>退出登录</span>
        </Space>
      ),
      value: 'logout'
    }
  ];

  const handleDropdownClick = (data: any) => {
    switch (data.value) {
      case 'blog':
        window.location.href = '/';
        break;
      case 'settings':
        window.location.href = '/admin/settings';
        break;
      case 'logout':
        // Handle logout logic
        localStorage.removeItem('admin_token');
        window.location.href = '/admin/login';
        break;
      default:
        break;
    }
  };

  return (
    <div style={{
      height: '64px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 24px',
      backgroundColor: 'var(--td-bg-color-container)',
      borderBottom: '1px solid var(--td-border-level-1-color)'
    }}>
      {/* Breadcrumb */}
      <div style={{ flex: 1 }}>
        <Breadcrumb>
          {getBreadcrumbItems().map((item, index) => (
            <BreadcrumbItem key={item.path}>
              {index === getBreadcrumbItems().length - 1 ? (
                <span style={{ color: 'var(--td-text-color-primary)', fontWeight: 500 }}>
                  {item.name}
                </span>
              ) : (
                <Link
                  to={item.path}
                  style={{ color: 'var(--td-text-color-secondary)' }}
                >
                  {item.name}
                </Link>
              )}
            </BreadcrumbItem>
          ))}
        </Breadcrumb>
      </div>

      {/* Header Actions */}
      <div>
        <Space>
          {/* Quick Actions */}
          <Button
            theme="primary"
            size="small"
            onClick={() => window.location.href = '/admin/posts/new'}
          >
            新建文章
          </Button>

          {/* User Dropdown */}
          <Dropdown
            options={dropdownOptions}
            onClick={handleDropdownClick}
            trigger="click"
          >
            <Avatar size="small" style={{ cursor: 'pointer' }}>
              <UserIcon />
            </Avatar>
          </Dropdown>
        </Space>
      </div>
    </div>
  );
}
