import { useState, useRef } from 'react';
import { Editor } from '@monaco-editor/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/utils/common';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  className?: string;
  disabled?: boolean;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = 'Start writing your post content...',
  height = '500px',
  className,
  disabled = false,
}: RichTextEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSplitView, setIsSplitView] = useState(false);
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // 配置编辑器选项
    editor.updateOptions({
      wordWrap: 'on',
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      lineHeight: 1.6,
      fontFamily: '"Fira Code", "JetBrains Mono", Consolas, "Courier New", monospace',
      theme: 'vs-dark',
    });

    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyP, () => {
      setIsPreviewMode(!isPreviewMode);
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyP, () => {
      setIsSplitView(!isSplitView);
    });
  };

  const insertMarkdown = (before: string, after: string = '') => {
    if (!editorRef.current) return;

    const editor = editorRef.current;
    const selection = editor.getSelection();
    const selectedText = editor.getModel().getValueInRange(selection);
    
    const newText = `${before}${selectedText}${after}`;
    
    editor.executeEdits('', [{
      range: selection,
      text: newText,
    }]);

    // 重新聚焦编辑器
    editor.focus();
  };

  const toolbarButtons = [
    {
      icon: '**B**',
      title: 'Bold (Ctrl+B)',
      action: () => insertMarkdown('**', '**'),
    },
    {
      icon: '*I*',
      title: 'Italic (Ctrl+I)',
      action: () => insertMarkdown('*', '*'),
    },
    {
      icon: '~~S~~',
      title: 'Strikethrough',
      action: () => insertMarkdown('~~', '~~'),
    },
    {
      icon: '`C`',
      title: 'Inline Code',
      action: () => insertMarkdown('`', '`'),
    },
    {
      icon: '```',
      title: 'Code Block',
      action: () => insertMarkdown('\n```\n', '\n```\n'),
    },
    {
      icon: '#',
      title: 'Heading',
      action: () => insertMarkdown('# '),
    },
    {
      icon: '•',
      title: 'Bullet List',
      action: () => insertMarkdown('- '),
    },
    {
      icon: '1.',
      title: 'Numbered List',
      action: () => insertMarkdown('1. '),
    },
    {
      icon: '🔗',
      title: 'Link',
      action: () => insertMarkdown('[', '](url)'),
    },
    {
      icon: '📷',
      title: 'Image',
      action: () => insertMarkdown('![alt](', ')'),
    },
    {
      icon: '|',
      title: 'Table',
      action: () => insertMarkdown('\n| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n'),
    },
    {
      icon: '>',
      title: 'Quote',
      action: () => insertMarkdown('> '),
    },
  ];

  const renderMarkdown = (content: string) => (
    <div className="prose prose-invert max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          code({ className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '');
            const isInline = !match;
            return !isInline ? (
              <SyntaxHighlighter
                style={vscDarkPlus as any}
                language={match[1]}
                PreTag="div"
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );

  return (
    <div className={cn('border border-gray-700 rounded-lg overflow-hidden bg-gray-900', className)}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center space-x-1">
          {toolbarButtons.map((button, index) => (
            <button
              key={index}
              onClick={button.action}
              title={button.title}
              disabled={disabled}
              className="px-2 py-1 text-xs font-mono text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {button.icon}
            </button>
          ))}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => {
              setIsPreviewMode(false);
              setIsSplitView(!isSplitView);
            }}
            className={cn(
              'px-3 py-1 text-xs rounded transition-colors',
              isSplitView && !isPreviewMode
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            )}
            disabled={disabled}
          >
            Split
          </button>
          <button
            onClick={() => {
              setIsSplitView(false);
              setIsPreviewMode(!isPreviewMode);
            }}
            className={cn(
              'px-3 py-1 text-xs rounded transition-colors',
              isPreviewMode && !isSplitView
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            )}
            disabled={disabled}
          >
            Preview
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="relative" style={{ height }}>
        {isPreviewMode && !isSplitView ? (
          // Preview only mode
          <div className="h-full overflow-auto p-4 bg-gray-900">
            {value ? (
              renderMarkdown(value)
            ) : (
              <div className="text-gray-500 italic">Nothing to preview yet...</div>
            )}
          </div>
        ) : isSplitView ? (
          // Split view mode
          <div className="flex h-full">
            <div className="flex-1 border-r border-gray-700">
              <Editor
                height="100%"
                defaultLanguage="markdown"
                value={value}
                onChange={(val) => onChange(val || '')}
                onMount={handleEditorDidMount}
                theme="vs-dark"
                options={{
                  readOnly: disabled,
                  placeholder,
                }}
              />
            </div>
            <div className="flex-1 overflow-auto p-4 bg-gray-900">
              {value ? (
                renderMarkdown(value)
              ) : (
                <div className="text-gray-500 italic">Nothing to preview yet...</div>
              )}
            </div>
          </div>
        ) : (
          // Editor only mode
          <Editor
            height="100%"
            defaultLanguage="markdown"
            value={value}
            onChange={(val) => onChange(val || '')}
            onMount={handleEditorDidMount}
            theme="vs-dark"
            options={{
              readOnly: disabled,
              placeholder,
            }}
          />
        )}
      </div>

      {/* Status bar */}
      <div className="flex items-center justify-between px-3 py-2 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex items-center space-x-4">
          <span>Lines: {value.split('\n').length}</span>
          <span>Characters: {value.length}</span>
          <span>Words: {value.trim() ? value.trim().split(/\s+/).length : 0}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>Markdown</span>
          <span>•</span>
          <span>UTF-8</span>
        </div>
      </div>
    </div>
  );
}
