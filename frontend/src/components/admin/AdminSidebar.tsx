import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, Avatar, Space } from 'tdesign-react';
import {
  DashboardIcon,
  ArticleIcon,
  TagIcon,
  ImageIcon,
  SettingIcon,
  UserIcon
} from 'tdesign-icons-react';
import { ADMIN_NAV_ITEMS } from '@/constants';

const { MenuItem } = Menu;

const iconMap = {
  'home': DashboardIcon,
  'document-text': ArticleIcon,
  'tag': TagIcon,
  'photo': ImageIcon,
  'cog': SettingIcon,
};

export default function AdminSidebar() {
  const navigate = useNavigate();
  const location = useLocation();

  const handleMenuClick = (value: string) => {
    navigate(value);
  };

  const getCurrentValue = () => {
    const currentItem = ADMIN_NAV_ITEMS.find(item =>
      location.pathname === item.path ||
      (item.path !== '/admin' && location.pathname.startsWith(`${item.path}/`))
    );
    return currentItem?.path || '/admin';
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div style={{ padding: '24px', borderBottom: '1px solid var(--td-border-level-1-color)' }}>
        <h1 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: 'var(--td-brand-color)',
          marginBottom: '4px',
          margin: 0
        }}>
          Cyrus Admin
        </h1>
        <p style={{
          fontSize: '12px',
          color: 'var(--td-text-color-secondary)',
          margin: 0
        }}>
          Content Management
        </p>
      </div>

      {/* Navigation */}
      <div style={{ flex: 1, padding: '16px 0' }}>
        <Menu
          value={getCurrentValue()}
          onChange={handleMenuClick}
          style={{ border: 'none' }}
        >
          {ADMIN_NAV_ITEMS.map((item) => {
            const IconComponent = iconMap[item.icon as keyof typeof iconMap];
            return (
              <MenuItem key={item.path} value={item.path}>
                <Space>
                  {IconComponent && <IconComponent />}
                  <div>
                    <div style={{ fontWeight: 500 }}>{item.name}</div>
                    <div style={{
                      fontSize: '12px',
                      color: 'var(--td-text-color-secondary)',
                      marginTop: '2px'
                    }}>
                      {item.description}
                    </div>
                  </div>
                </Space>
              </MenuItem>
            );
          })}
        </Menu>
      </div>

      {/* User Info & Actions */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid var(--td-border-level-1-color)',
        marginTop: 'auto'
      }}>
        {/* User Profile */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          padding: '12px',
          borderRadius: '8px',
          backgroundColor: 'var(--td-bg-color-component)',
          border: '1px solid var(--td-border-level-1-color)',
          marginBottom: '12px'
        }}>
          <Avatar size="small" style={{ marginRight: '12px' }}>
            <UserIcon />
          </Avatar>
          <div style={{ flex: 1 }}>
            <div style={{
              fontSize: '14px',
              fontWeight: 500,
              color: 'var(--td-text-color-primary)'
            }}>
              Cyrus
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--td-text-color-secondary)'
            }}>
              Administrator
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


