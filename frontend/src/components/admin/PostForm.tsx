import { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Textarea,
  DatePicker,
  Button,
  Card,
  Tag,
  Space,
  Row,
  Col,
  FormInstanceFunctions,
  SubmitContext
} from 'tdesign-react';
import type { BlogPost } from '@/types/blog';

interface PostFormData {
  title: string;
  excerpt: string;
  slug: string;
  date: string;
  categories: string[];
}

interface PostFormProps {
  initialData?: Partial<BlogPost>;
  onSubmit: (data: PostFormData) => void;
  onCancel: () => void;
  loading?: boolean;
  submitText?: string;
  className?: string;
}

const { FormItem } = Form;

export default function PostForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  submitText = 'Save Post',
  className,
}: PostFormProps) {
  const [formData, setFormData] = useState<PostFormData>({
    title: initialData?.title || '',
    excerpt: initialData?.excerpt || '',
    slug: initialData?.slug || '',
    date: initialData?.date || new Date().toISOString().split('T')[0],
    categories: initialData?.categories || [],
  });

  const [availableCategories, setAvailableCategories] = useState<string[]>([
    'Technology',
    'Programming',
    'Web Development',
    'React',
    'TypeScript',
    'Rust',
    'Tutorial',
    'Tips',
    'Personal',
    'Review',
  ]);
  const [newCategory, setNewCategory] = useState('');
  const [isAddingCategory, setIsAddingCategory] = useState(false);

  // Auto-generate slug from title
  useEffect(() => {
    if (!initialData?.slug && formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.title, initialData?.slug]);

  const handleSubmit = (context: SubmitContext) => {
    if (context.validateResult === true) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: keyof PostFormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addCategory = () => {
    if (newCategory.trim() && !availableCategories.includes(newCategory.trim())) {
      setAvailableCategories(prev => [...prev, newCategory.trim()]);
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()],
      }));
      setNewCategory('');
      setIsAddingCategory(false);
    }
  };

  const toggleCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category],
    }));
  };

  const removeCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter(c => c !== category),
    }));
  };

  return (
    <Card className={className} style={{ width: '100%' }}>
      <Form
        onSubmit={handleSubmit}
        labelWidth="120px"
        layout="vertical"
        preventSubmitDefault
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <FormItem
              label="标题"
              name="title"
              rules={[
                { required: true, message: '请输入文章标题' },
                { min: 3, message: '标题至少需要3个字符' },
                { max: 200, message: '标题不能超过200个字符' }
              ]}
            >
              <Input
                value={formData.title}
                onChange={(value) => handleInputChange('title', value)}
                placeholder="请输入文章标题..."
                disabled={loading}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label="URL别名"
              name="slug"
              rules={[
                { required: true, message: '请输入URL别名' },
                { pattern: /^[a-z0-9-]+$/, message: 'URL别名只能包含小写字母、数字和连字符' }
              ]}
            >
              <Input
                value={formData.slug}
                onChange={(value) => handleInputChange('slug', value.toLowerCase())}
                placeholder="url-slug"
                disabled={loading}
              />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={24}>
            <FormItem
              label="文章摘要"
              name="excerpt"
              rules={[
                { required: true, message: '请输入文章摘要' },
                { min: 10, message: '摘要至少需要10个字符' },
                { max: 500, message: '摘要不能超过500个字符' }
              ]}
            >
              <Textarea
                value={formData.excerpt}
                onChange={(value) => handleInputChange('excerpt', value)}
                placeholder="请输入文章摘要..."
                rows={3}
                disabled={loading}
                maxlength={500}
              />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <FormItem
              label="发布日期"
              name="date"
              rules={[{ required: true, message: '请选择发布日期' }]}
            >
              <DatePicker
                value={formData.date}
                onChange={(value) => handleInputChange('date', String(value || ''))}
                disabled={loading}
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label="分类标签"
              name="categories"
              rules={[{ required: true, message: '请至少选择一个分类' }]}
            >
              <div>
                {/* Selected Categories */}
                {formData.categories.length > 0 && (
                  <div style={{ marginBottom: '12px' }}>
                    <Space>
                      {formData.categories.map((category) => (
                        <Tag
                          key={category}
                          closable
                          onClose={() => removeCategory(category)}
                          theme="primary"
                        >
                          {category}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                )}

                {/* Available Categories */}
                <div style={{ marginBottom: '12px' }}>
                  <Space>
                    {availableCategories
                      .filter(cat => !formData.categories.includes(cat))
                      .map((category) => (
                        <Tag
                          key={category}
                          variant="outline"
                          onClick={() => toggleCategory(category)}
                          style={{ cursor: 'pointer' }}
                        >
                          + {category}
                        </Tag>
                      ))}
                  </Space>
                </div>

                {/* Add New Category */}
                {isAddingCategory ? (
                  <Space>
                    <Input
                      value={newCategory}
                      onChange={setNewCategory}
                      placeholder="新分类名称..."
                      onEnter={addCategory}
                      disabled={loading}
                      size="small"
                    />
                    <Button
                      size="small"
                      theme="primary"
                      onClick={addCategory}
                      disabled={!newCategory.trim() || loading}
                    >
                      添加
                    </Button>
                    <Button
                      size="small"
                      variant="text"
                      onClick={() => {
                        setIsAddingCategory(false);
                        setNewCategory('');
                      }}
                      disabled={loading}
                    >
                      取消
                    </Button>
                  </Space>
                ) : (
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => setIsAddingCategory(true)}
                    disabled={loading}
                  >
                    + 添加新分类
                  </Button>
                )}
              </div>
            </FormItem>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Space>
              <Button
                type="submit"
                theme="primary"
                loading={loading}
                disabled={loading}
              >
                {submitText}
              </Button>
              <Button
                variant="base"
                onClick={onCancel}
                disabled={loading}
              >
                取消
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Card>
  );
}
