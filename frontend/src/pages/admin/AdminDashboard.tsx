import { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space
} from 'tdesign-react';
import {
  ArticleIcon,
  TagIcon,
  ViewListIcon,
  TrendingUpIcon
} from 'tdesign-icons-react';

export default function AdminDashboard() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Welcome Section */}
      <Card>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '24px'
        }}>
          <div>
            <h1 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: 'var(--td-text-color-primary)',
              marginBottom: '8px',
              margin: 0
            }}>
              欢迎回来，Cyrus! 👋
            </h1>
            <p style={{
              color: 'var(--td-text-color-secondary)',
              margin: 0
            }}>
              这是您博客今天的情况概览
            </p>
          </div>
          <div>
            <Button
              theme="primary"
              onClick={() => window.location.href = '/admin/posts/new'}
            >
              新建文章
            </Button>
          </div>
        </div>
      </Card>

      {/* Stats Cards */}
      <Row gutter={[24, 24]}>
        <Col span={6}>
          <Card style={{ textAlign: 'center' }}>
            <Statistic
              title="总文章数"
              value={12}
              prefix={<ArticleIcon />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: 'center' }}>
            <Statistic
              title="分类数"
              value={5}
              prefix={<TagIcon />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: 'center' }}>
            <Statistic
              title="已发布"
              value={10}
              prefix={<ViewListIcon />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: 'center' }}>
            <Statistic
              title="总浏览量"
              value={1234}
              prefix={<TrendingUpIcon />}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row gutter={[24, 24]}>
        <Col span={16}>
          <Card title="快速操作">
            <Space>
              <Button
                theme="primary"
                onClick={() => window.location.href = '/admin/posts/new'}
              >
                新建文章
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin/categories'}
              >
                管理分类
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin/media'}
              >
                上传媒体
              </Button>
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="系统状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>服务器状态</span>
                <span style={{ color: 'var(--td-success-color)' }}>正常</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>数据库</span>
                <span style={{ color: 'var(--td-success-color)' }}>已连接</span>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
