import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, CardContent, LoadingSpinner } from '@/components/ui';
import { useToast } from '@/components/common/Toast';
import RichTextEditor from '@/components/admin/RichTextEditor';
import PostForm from '@/components/admin/PostForm';
import type { BlogPost } from '@/types/blog';
import { adminApi } from '@/services/api';

interface PostFormData {
  title: string;
  excerpt: string;
  slug: string;
  date: string;
  categories: string[];
}

export default function AdminPostEditor() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [post, setPost] = useState<BlogPost | null>(null);
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const isEditing = slug !== 'new';

  // Load existing post for editing
  useEffect(() => {
    const loadPost = async () => {
      if (!isEditing) {
        setInitialLoading(false);
        return;
      }

      try {
        setLoading(true);
        const postData = await adminApi.getPost(slug);
        setPost(postData);
        setContent(postData.content || '');
      } catch (error) {
        console.error('Error loading post:', error);
        showToast('Failed to load post', 'error');
        navigate('/admin/posts');
      } finally {
        setLoading(false);
        setInitialLoading(false);
      }
    };

    loadPost();
  }, [slug, isEditing, navigate, showToast]);

  // Track unsaved changes
  useEffect(() => {
    const originalContent = post?.content || '';
    setHasUnsavedChanges(content !== originalContent);
  }, [content, post?.content]);

  // Warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleFormSubmit = async (formData: PostFormData) => {
    if (!content.trim()) {
      showToast('Content is required', 'error');
      return;
    }

    try {
      setSaving(true);

      const postData = {
        ...formData,
        content,
      };

      let result;
      if (isEditing) {
        result = await adminApi.updatePost(slug, postData);
      } else {
        result = await adminApi.createPost(postData);
      }

      if (result.success) {
        showToast(
          isEditing ? 'Post updated successfully' : 'Post created successfully',
          'success'
        );
        setHasUnsavedChanges(false);
        
        if (!isEditing && result.post) {
          // Redirect to edit mode for the newly created post
          navigate(`/admin/posts/${result.post.slug}`, { replace: true });
        }
      } else {
        showToast(result.message || 'Failed to save post', 'error');
      }
    } catch (error) {
      console.error('Error saving post:', error);
      showToast('An error occurred while saving the post', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        navigate('/admin/posts');
      }
    } else {
      navigate('/admin/posts');
    }
  };

  const handleSaveDraft = async () => {
    // For now, just save the post - in the future could implement draft status
    const formElement = document.querySelector('form') as HTMLFormElement;
    if (formElement) {
      formElement.requestSubmit();
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-2xl font-bold text-white">
            {isEditing ? 'Edit Post' : 'Create New Post'}
          </h1>
          <p className="text-gray-400 mt-1">
            {isEditing 
              ? `Editing: ${post?.title || 'Untitled'}`
              : 'Create a new blog post with rich content'
            }
          </p>
        </div>

        <div className="flex items-center gap-3">
          {hasUnsavedChanges && (
            <span className="text-sm text-yellow-400 flex items-center">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
              Unsaved changes
            </span>
          )}
          
          <Button
            variant="ghost"
            onClick={handleSaveDraft}
            disabled={saving || !content.trim()}
            loading={saving}
          >
            Save Draft
          </Button>
          
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={saving}
          >
            Cancel
          </Button>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
        {/* Post Form - Left Side */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="xl:col-span-2"
        >
          <PostForm
            initialData={post || undefined}
            onSubmit={handleFormSubmit}
            onCancel={handleCancel}
            loading={saving}
            submitText={isEditing ? 'Update Post' : 'Create Post'}
          />
        </motion.div>

        {/* Content Editor - Right Side */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="xl:col-span-3"
        >
          <Card>
            <CardContent className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Content *
                </label>
                <p className="text-xs text-gray-500 mb-4">
                  Write your post content using Markdown. Use the toolbar for quick formatting.
                </p>
              </div>
              
              <RichTextEditor
                value={content}
                onChange={setContent}
                height="600px"
                disabled={saving}
                placeholder="Start writing your amazing post content here..."
              />
              
              {!content.trim() && (
                <p className="mt-2 text-sm text-red-400">
                  Content is required
                </p>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-300 mb-2">Quick Tips</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-xs text-gray-500">
              <div>
                <strong className="text-gray-400">Ctrl+P:</strong> Toggle preview
              </div>
              <div>
                <strong className="text-gray-400">Ctrl+Shift+P:</strong> Split view
              </div>
              <div>
                <strong className="text-gray-400">Ctrl+S:</strong> Save draft
              </div>
              <div>
                <strong className="text-gray-400">Markdown:</strong> Supported with syntax highlighting
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
