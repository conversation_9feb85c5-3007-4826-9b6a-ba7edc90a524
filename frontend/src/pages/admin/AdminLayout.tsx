import { Outlet, Navigate } from 'react-router-dom';
import { Layout } from 'tdesign-react';
import { useAdminGuard } from '@/hooks/useAuth.tsx';
import AdminHeader from '@/components/admin/AdminHeader';
import AdminSidebar from '@/components/admin/AdminSidebar';
import { LoadingSpinner } from '@/components/ui';

const { Header, Aside, Content } = Layout;

export default function AdminLayout() {
  const { isAuthenticated, isLoading, shouldRedirect } = useAdminGuard();

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 重定向到登录页
  if (shouldRedirect) {
    return <Navigate to="/admin/login" replace />;
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Aside width="280px">
        <AdminSidebar />
      </Aside>
      <Layout>
        <Header style={{ padding: 0, height: '64px' }}>
          <AdminHeader />
        </Header>
        <Content style={{ padding: '24px', overflow: 'auto' }}>
          <div style={{ maxWidth: '1400px', margin: '0 auto', width: '100%' }}>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
