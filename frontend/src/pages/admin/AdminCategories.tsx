import { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Dialog,
  Form,
  Space,
  Tag,
  Row,
  Col,
  ColorPicker
} from 'tdesign-react';
import {
  AddIcon,
  EditIcon,
  DeleteIcon,
  TagIcon
} from 'tdesign-icons-react';

const { FormItem } = Form;

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  postCount: number;
  color: string;
}

export default function AdminCategories() {
  const [categories, setCategories] = useState<Category[]>([
    {
      id: '1',
      name: 'Technology',
      slug: 'technology',
      description: 'Articles about latest technology trends and innovations',
      postCount: 12,
      color: '#3b82f6'
    },
    {
      id: '2',
      name: 'Business',
      slug: 'business',
      description: 'Business strategy and entrepreneurship insights',
      postCount: 8,
      color: '#10b981'
    },
    {
      id: '3',
      name: 'Leadership',
      slug: 'leadership',
      description: 'Leadership principles and management techniques',
      postCount: 6,
      color: '#f59e0b'
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3b82f6'
  });

  const handleCreateCategory = () => {
    setFormData({ name: '', description: '', color: '#3b82f6' });
    setEditingCategory(null);
    setShowCreateModal(true);
  };

  const handleEditCategory = (category: Category) => {
    setFormData({
      name: category.name,
      description: category.description,
      color: category.color
    });
    setEditingCategory(category);
    setShowCreateModal(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      if (editingCategory) {
        // Update existing category
        setCategories(prev => prev.map(cat => 
          cat.id === editingCategory.id 
            ? { ...cat, ...formData, slug: formData.name.toLowerCase().replace(/\s+/g, '-') }
            : cat
        ));
      } else {
        // Create new category
        const newCategory: Category = {
          id: Date.now().toString(),
          ...formData,
          slug: formData.name.toLowerCase().replace(/\s+/g, '-'),
          postCount: 0
        };
        setCategories(prev => [...prev, newCategory]);
      }
      setShowCreateModal(false);
      setLoading(false);
    }, 1000);
  };

  const handleDeleteCategory = (categoryId: string) => {
    setCategories(prev => prev.filter(cat => cat.id !== categoryId));
  };

  const colorOptions = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', 
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6b7280'
  ];

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0, marginBottom: '8px' }}>
              分类管理
            </h1>
            <p style={{ color: 'var(--td-text-color-secondary)', margin: 0 }}>
              使用分类组织您的内容。创建、编辑和管理您的博客分类。
            </p>
          </Col>
          <Col>
            <Button
              theme="primary"
              icon={<AddIcon />}
              onClick={handleCreateCategory}
            >
              添加分类
            </Button>
          </Col>
        </Row>
      {/* Categories Table */}
      <Card title="分类列表">
        <Table
          data={categories.map(category => ({
            key: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
            postCount: category.postCount,
            color: category.color
          }))}
          columns={[
            {
              colKey: 'name',
              title: '分类名称',
              width: 150,
              cell: ({ row }) => (
                <Space>
                  <div
                    style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: row.color
                    }}
                  />
                  <span style={{ fontWeight: 500 }}>{row.name}</span>
                </Space>
              )
            },
            {
              colKey: 'slug',
              title: 'URL别名',
              width: 120,
              cell: ({ row }) => (
                <Tag variant="outline" size="small">{row.slug}</Tag>
              )
            },
            {
              colKey: 'description',
              title: '描述',
              ellipsis: true,
              cell: ({ row }) => row.description
            },
            {
              colKey: 'postCount',
              title: '文章数量',
              width: 100,
              align: 'center',
              cell: ({ row }) => (
                <Tag theme="primary" size="small">{row.postCount}</Tag>
              )
            },
            {
              colKey: 'actions',
              title: '操作',
              width: 150,
              cell: ({ row }) => (
                <Space>
                  <Button
                    theme="primary"
                    variant="text"
                    size="small"
                    icon={<EditIcon />}
                    onClick={() => handleEditCategory(row)}
                  >
                    编辑
                  </Button>
                  <Button
                    theme="danger"
                    variant="text"
                    size="small"
                    icon={<DeleteIcon />}
                    onClick={() => handleDeleteCategory(row.key)}
                  >
                    删除
                  </Button>
                </Space>
              )
            }
          ]}
        />
      {/* Create/Edit Category Dialog */}
      <Dialog
        visible={showCreateModal}
        header={editingCategory ? '编辑分类' : '创建分类'}
        onClose={() => {
          setShowCreateModal(false);
          setEditingCategory(null);
          setFormData({ name: '', description: '', color: '#3b82f6' });
        }}
        onConfirm={handleSubmit}
        confirmBtn={{ content: editingCategory ? '更新' : '创建', loading }}
        cancelBtn={{ content: '取消' }}
      >
        <Form>
          <FormItem label="分类名称" name="name">
            <Input
              value={formData.name}
              onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
              placeholder="请输入分类名称"
            />
          </FormItem>
          <FormItem label="描述" name="description">
            <Input
              value={formData.description}
              onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
              placeholder="请输入分类描述"
            />
          </FormItem>
          <FormItem label="颜色" name="color">
            <ColorPicker
              value={formData.color}
              onChange={(value) => setFormData(prev => ({ ...prev, color: value }))}
            />
          </FormItem>
        </Form>
      </Dialog>
    </div>
  );

  function handleEditCategory(category: any) {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description,
      color: category.color
    });
    setShowCreateModal(true);
  }
}
