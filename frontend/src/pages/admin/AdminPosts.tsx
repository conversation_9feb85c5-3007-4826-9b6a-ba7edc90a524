import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Loading,
  Alert,
  Row,
  Col
} from 'tdesign-react';
import {
  AddIcon,
  RefreshIcon,
  SearchIcon,
  EditIcon,
  DeleteIcon
} from 'tdesign-icons-react';
import type { BlogPost } from '@/types/blog';
import { adminApi } from '@/services/api';

export default function AdminPosts() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  // 获取文章数据的函数
  const fetchPosts = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const data = await adminApi.getAllPosts();
      setPosts(data.posts);
    } catch (error) {
      console.error('Error fetching posts:', error);
      setError('Failed to load posts. Please check your connection and try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  const handlePostDeleted = (slug: string) => {
    setPosts(posts.filter(post => post.slug !== slug));
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchPosts(true);
  };

  // 获取所有分类
  const categories = Array.from(new Set(posts.flatMap(post => post.categories)));

  // 过滤和排序文章
  const filteredAndSortedPosts = posts
    .filter(post => {
      const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || post.categories.includes(selectedCategory);
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'date':
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
    });

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Loading size="large" text="加载中..." />
      </div>
    );
  }

  if (error && posts.length === 0) {
    return (
      <Alert
        theme="error"
        message="加载失败"
        description={error}
        action={
          <Space>
            <Button
              theme="primary"
              size="small"
              onClick={() => fetchPosts()}
              loading={loading}
            >
              重试
            </Button>
            <Button
              variant="outline"
              size="small"
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
          </Space>
        }
      />
    );
  }

  const handleDeletePost = (slug: string) => {
    // TODO: 实现删除逻辑
    console.log('Delete post:', slug);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Error Banner */}
      {error && posts.length > 0 && (
        <Alert theme="warning" message={error} closable onClose={() => setError(null)} />
      )}

      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>文章管理</h1>
          </Col>
          <Col>
            <Space>
              <Button
                theme="primary"
                icon={<AddIcon />}
                onClick={() => window.location.href = '/admin/posts/new'}
              >
                新建文章
              </Button>
              <Button
                variant="outline"
                icon={<RefreshIcon />}
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      {/* Search and Filter */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col span={8}>
            <Input
              placeholder="搜索文章标题..."
              value={searchTerm}
              onChange={setSearchTerm}
              prefixIcon={<SearchIcon />}
            />
          </Col>
          <Col span={6}>
            <Select
              value={selectedCategory}
              onChange={setSelectedCategory}
              placeholder="选择分类"
              style={{ width: '100%' }}
              options={[
                { label: '全部分类', value: 'all' },
                { label: '技术', value: 'Technology' },
                { label: '编程', value: 'Programming' },
                { label: 'React', value: 'React' },
                { label: 'TypeScript', value: 'TypeScript' }
              ]}
            />
          </Col>
          <Col span={6}>
            <Select
              value={sortBy}
              onChange={setSortBy}
              placeholder="排序方式"
              style={{ width: '100%' }}
              options={[
                { label: '按日期排序', value: 'date' },
                { label: '按标题排序', value: 'title' },
                { label: '按分类排序', value: 'category' }
              ]}
            />
          </Col>
          <Col span={4}>
            <span style={{ color: 'var(--td-text-color-secondary)' }}>
              共 {filteredPosts.length} 篇文章
            </span>
          </Col>
        </Row>
      </Card>

      {/* Posts Table */}
      <Card title="文章列表">
        <Table
          data={filteredPosts.map(post => ({
            key: post.slug,
            title: post.title,
            excerpt: post.excerpt,
            categories: post.categories,
            date: post.date,
            slug: post.slug
          }))}
          columns={[
            {
              colKey: 'title',
              title: '标题',
              width: 300,
              ellipsis: true,
              cell: ({ row }) => (
                <div>
                  <div style={{ fontWeight: 500, marginBottom: '4px' }}>
                    {row.title}
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: 'var(--td-text-color-secondary)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {row.excerpt}
                  </div>
                </div>
              )
            },
            {
              colKey: 'categories',
              title: '分类',
              width: 200,
              cell: ({ row }) => (
                <Space>
                  {row.categories.slice(0, 2).map((category: string, idx: number) => (
                    <Tag key={idx} variant="outline" size="small">
                      {category}
                    </Tag>
                  ))}
                  {row.categories.length > 2 && (
                    <Tag variant="outline" size="small">
                      +{row.categories.length - 2}
                    </Tag>
                  )}
                </Space>
              )
            },
            {
              colKey: 'date',
              title: '发布日期',
              width: 120,
              cell: ({ row }) => new Date(row.date).toLocaleDateString()
            },
            {
              colKey: 'actions',
              title: '操作',
              width: 150,
              cell: ({ row }) => (
                <Space>
                  <Button
                    theme="primary"
                    variant="text"
                    size="small"
                    icon={<EditIcon />}
                    onClick={() => window.location.href = `/admin/posts/${row.slug}`}
                  >
                    编辑
                  </Button>
                  <Button
                    theme="danger"
                    variant="text"
                    size="small"
                    icon={<DeleteIcon />}
                    onClick={() => handleDeletePost(row.slug)}
                  >
                    删除
                  </Button>
                </Space>
              )
            }
          ]}
          pagination={{
            current: 1,
            pageSize: 10,
            total: filteredPosts.length,
            showJumper: true,
            showSizeChanger: true
          }}
        />
      </Card>
    </div>
  );
}
