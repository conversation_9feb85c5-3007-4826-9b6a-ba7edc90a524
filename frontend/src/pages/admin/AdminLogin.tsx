import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Space,
  Alert,
  Row,
  Col
} from 'tdesign-react';
import {
  LockOnIcon,
  UserIcon,
  EyeIcon,
  EyeOffIcon
} from 'tdesign-icons-react';
import { useAuth } from '@/hooks/useAuth.tsx';

const { FormItem } = Form;

export default function AdminLogin() {
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!token.trim()) {
      setError('Please enter a token');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const success = await login(token);
      if (success) {
        navigate('/admin');
      } else {
        setError('Invalid token');
      }
    } catch (error) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: 'var(--td-bg-color-page)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '24px'
    }}>
      <Row justify="center" style={{ width: '100%' }}>
        <Col span={8}>
          <Card style={{ maxWidth: '400px', margin: '0 auto' }}>
            {/* Header */}
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <Link to="/" style={{ textDecoration: 'none' }}>
                <h1 style={{
                  fontSize: '28px',
                  fontWeight: 'bold',
                  color: 'var(--td-brand-color)',
                  marginBottom: '8px',
                  margin: 0
                }}>
                  Cyrus
                </h1>
              </Link>
              <h2 style={{
                fontSize: '20px',
                fontWeight: 600,
                color: 'var(--td-text-color-primary)',
                marginBottom: '8px',
                margin: 0
              }}>
                管理员门户
              </h2>
              <p style={{
                color: 'var(--td-text-color-secondary)',
                margin: 0
              }}>
                输入访问令牌以继续
              </p>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert theme="error" message={error} style={{ marginBottom: '24px' }} />
            )}

            {/* Login Form */}
            <Form onSubmit={handleSubmit} preventSubmitDefault>
              <FormItem
                label="访问令牌"
                name="token"
                rules={[{ required: true, message: '请输入访问令牌' }]}
              >
                <Input
                  type={showPassword ? "text" : "password"}
                  value={token}
                  onChange={setToken}
                  placeholder="请输入管理员令牌"
                  disabled={loading}
                  prefixIcon={<LockOnIcon />}
                  suffixIcon={
                    <Button
                      variant="text"
                      size="small"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={loading}
                      style={{ padding: 0, minWidth: 'auto' }}
                    >
                      {showPassword ? <EyeOffIcon /> : <EyeIcon />}
                    </Button>
                  }
                />
              </FormItem>

              <FormItem>
                <Button
                  type="submit"
                  theme="primary"
                  block
                  loading={loading}
                  disabled={loading || !token.trim()}
                >
                  {loading ? '登录中...' : '登录'}
                </Button>
              </FormItem>
            </Form>

            {/* Security Notice */}
            <div style={{
              marginTop: '24px',
              padding: '16px',
              backgroundColor: 'var(--td-bg-color-component)',
              border: '1px solid var(--td-border-level-1-color)',
              borderRadius: '8px'
            }}>
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                <LockOnIcon style={{
                  color: 'var(--td-brand-color)',
                  marginRight: '8px',
                  marginTop: '2px',
                  flexShrink: 0
                }} />
                <div>
                  <p style={{
                    fontSize: '14px',
                    color: 'var(--td-brand-color)',
                    fontWeight: 500,
                    margin: 0,
                    marginBottom: '4px'
                  }}>
                    安全访问
                  </p>
                  <p style={{
                    fontSize: '12px',
                    color: 'var(--td-text-color-secondary)',
                    margin: 0
                  }}>
                    您的会话将被加密，并为了安全起见自动过期。
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Link
                to="/"
                style={{
                  color: 'var(--td-text-color-secondary)',
                  textDecoration: 'none',
                  fontSize: '14px',
                  display: 'inline-flex',
                  alignItems: 'center'
                }}
              >
                ← 返回博客
              </Link>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
